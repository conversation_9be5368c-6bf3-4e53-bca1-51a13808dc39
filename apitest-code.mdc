# API接口请求参数及业务状态码文档

## 📋 文档说明
- 本文档基于Controller系统性扫描生成，严格按照用户提供的模板格式，包含所有API接口的请求参数及业务状态码信息。

---

### **AuthController (6)**
- [x] **1** 用户注册 `POST /auth/register`
  - 请求参数：`username, password, email` (email可选)
  - 成功响应：`200` - 成功
  - 错误响应：`1005` - 用户已存在, `1011` - 系统错误

- [x] **2** 用户登录 `POST /auth/login`
  - 请求参数：`username, password`
  - 成功响应：`200` - success
  - 错误响应：`1004` - 用户未注册

- [x] **3** Token验证 `GET /auth/verify`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - Token验证成功
  - 错误响应：`401` - Token无效或过期

- [x] **4** 用户登出 `POST /auth/logout`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - 登出成功
  - 错误响应：`401` - 请登录后操作, `1011` - 登出失败，请重试

- [x] **5** 忘记密码 `POST /auth/forgot-password`
  - 请求参数：`email`
  - 成功响应：`200` - 密码重置邮件发送成功
  - 错误响应：`401` - 用户不存在, `1011` - 操作失败，请重试

- [x] **6** 重置密码 `POST /auth/reset-password`
  - 请求参数：`token, new_password, password_confirmation`
  - 成功响应：`200` - 密码重置成功
  - 错误响应：`401` - 重置令牌无效或已过期, `1011` - 密码重置失败，请重试

### **UserController (4)**
- [x] **7** 获取用户个人信息 `GET /user/profile`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `1011` - 系统错误

- [x] **8** 更新用户资料 `PUT /user/profile`
  - 请求参数：`nickname, email, avatar` (可选)
  - 成功响应：`200` - 用户资料更新成功
  - 错误响应：`401` - 认证失败, `422` - 参数验证失败, `1010` - 没有可更新的字段, `1009` - 邮箱已被使用, `1011` - 个人信息更新失败

- [x] **9** 更新用户偏好设置 `PUT /user/preferences`
  - 请求参数：`language, timezone, email_notifications, push_notifications, ai_preferences, ui_preferences, workflow_preferences, default_ai_model, auto_save_interval, show_tutorials` (可选)
  - 成功响应：`200` - 偏好设置更新成功
  - 错误响应：`401` - 认证失败, `1011` - 偏好设置更新失败

- [x] **10** 获取用户偏好设置 `GET /user/preferences`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `1011` - 系统错误

### **PointsController (3)**
- [x] **11** 积分余额查询 `GET /points/balance`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败

- [x] **12** 积分充值 `POST /points/recharge`
  - 请求参数：`amount, payment_method`
  - 成功响应：`200` - 充值成功
  - 错误响应：`401` - 认证失败, `1011` - 充值失败，请稍后重试

- [x] **13** 积分交易记录 `GET /points/transactions`
  - 请求参数：`page, per_page, status, business_type` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败

### **CreditsController (3)**
- [x] **14** 积分预检查 `POST /credits/check`
  - 请求参数：`amount, business_type, business_id` (可选)
  - 成功响应：`200` - 积分检查完成
  - 错误响应：`401` - 认证失败, `400` - 无效的业务类型, `1011` - 积分检查失败

- [x] **15** 积分冻结 `POST /credits/freeze`
  - 请求参数：`amount, business_type, business_id, timeout_seconds` (可选)
  - 成功响应：`200` - 积分冻结成功
  - 错误响应：`401` - 认证失败, `400` - 无效的业务类型, `1006` - 积分不足, `1011` - 积分冻结失败

- [x] **16** 积分返还 `POST /credits/refund`
  - 请求参数：`freeze_id, return_reason` (可选)
  - 成功响应：`200` - 积分返还成功
  - 错误响应：`401` - 认证失败, `404` - 找不到对应的业务记录, `409` - 积分已经被扣除, `1011` - 积分返还失败

### **UserGrowthController (10)**
- [x] **17** 获取用户成长信息 `GET /user-growth/profile`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `404` - 用户不存在, `1011` - 系统错误

- [x] **18** 获取排行榜 `GET /user-growth/leaderboard`
  - 请求参数：`type, period, limit` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `422` - 参数错误, `1011` - 系统错误

- [x] **19** 获取每日任务 `GET /user-growth/daily-tasks`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `1011` - 系统错误

- [x] **20** 获取成长历史 `GET /user-growth/history`
  - 请求参数：`type, date_from, date_to, page, per_page` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `422` - 参数错误, `1011` - 系统错误

- [x] **21** 获取统计信息 `GET /user-growth/statistics`
  - 请求参数：`period` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `422` - 参数错误, `1011` - 系统错误

- [x] **22** 设置成长目标 `POST /user-growth/goals`
  - 请求参数：`goals` (数组，包含type和target)
  - 成功响应：`200` - 成长目标设置成功
  - 错误响应：`401` - 认证失败, `422` - 参数错误, `1011` - 系统错误

- [x] **23** 获取推荐 `GET /user-growth/recommendations`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `1011` - 系统错误

- [x] **24** 获取里程碑 `GET /user-growth/milestones`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `1011` - 系统错误

- [x] **25** 完成每日任务 `POST /user-growth/daily-tasks/{id}/complete`
  - 请求参数：`task_id, completion_data` (可选)
  - 成功响应：`200` - 每日任务完成
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `422` - 参数错误, `1008` - 任务已完成, `1011` - 系统错误

- [x] **26** 完成成就 `POST /user-growth/achievements/{id}/complete`
  - 请求参数：`achievement_id, progress_data` (可选)
  - 成功响应：`200` - 成就完成
  - 错误响应：`401` - 认证失败, `404` - 成就不存在, `422` - 参数错误, `1008` - 成就已完成, `1011` - 系统错误

### **NotificationController (6)**
- [x] **27** 获取用户通知列表 `GET /notifications`
  - 请求参数：`type, status, page, per_page` (可选)
  - 成功响应：`200` - 用户通知获取成功
  - 错误响应：`401` - 认证失败, `422` - 参数错误, `1011` - 系统错误

- [x] **28** 标记通知为已读 `PUT /notifications/mark-read`
  - 请求参数：`notification_ids` (数组)
  - 成功响应：`200` - 通知已标记为已读
  - 错误响应：`401` - 认证失败, `422` - 参数错误, `1011` - 系统错误

- [x] **29** 标记所有通知为已读 `PUT /notifications/mark-all-read`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - 所有通知已标记为已读
  - 错误响应：`401` - 认证失败, `1011` - 系统错误

- [x] **30** 获取通知统计 `GET /notifications/stats`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - 通知统计获取成功
  - 错误响应：`401` - 认证失败, `1011` - 系统错误

- [x] **31** 删除通知 `DELETE /notifications/{id}`
  - 请求参数：通知ID (路径参数)
  - 成功响应：`200` - 通知删除成功
  - 错误响应：`401` - 认证失败, `404` - 通知不存在, `1011` - 系统错误

- [x] **32** 发送通知 `POST /notifications/send`
  - 请求参数：`recipients, title, content, type` (可选)
  - 成功响应：`200` - 通知发送成功
  - 错误响应：`401` - 认证失败, `403` - 权限不足，仅管理员可发送系统通知, `422` - 参数错误, `1092` - 发送频率超限，请稍后再试, `1011` - 系统错误

### **ConfigController (7)**
- [x] **33** 获取配置列表 `GET /config`
  - 请求参数：`category, key, page, per_page` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `403` - 权限不足，仅管理员可查看系统配置, `1011` - 系统错误

- [x] **34** 获取公开配置 `GET /config/public`
  - 请求参数：无
  - 成功响应：`200` - 成功
  - 错误响应：`1011` - 系统错误

- [x] **35** 更新配置 `PUT /config/{id}`
  - 请求参数：`value, description` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `403` - 权限不足，仅管理员可修改系统配置, `422` - 参数错误, `404` - 配置不存在, `1011` - 系统错误

- [x] **36** 批量更新配置 `PUT /config/batch`
  - 请求参数：`configs` (数组)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `403` - 权限不足，仅管理员可修改系统配置, `422` - 参数错误, `1011` - 系统错误

- [x] **37** 重置配置 `POST /config/{id}/reset`
  - 请求参数：配置ID (路径参数)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `403` - 权限不足，仅管理员可重置系统配置, `404` - 配置不存在, `1011` - 系统错误

- [x] **38** 获取配置历史 `GET /config/{id}/history`
  - 请求参数：配置ID (路径参数), `page, per_page` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `403` - 权限不足，仅管理员可查看配置历史, `422` - 参数错误, `404` - 配置不存在, `1011` - 系统错误

- [x] **39** 验证配置 `POST /config/validate`
  - 请求参数：`key, value, type` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `403` - 权限不足，仅管理员可验证配置, `422` - 参数错误, `1011` - 系统错误

### **AiTaskController (8)**
- [x] **40** 获取任务列表 `GET /tasks`
  - 请求参数：`status, type, page, per_page` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `1011` - 系统错误

- [x] **41** 获取任务详情 `GET /tasks/{id}`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `1011` - 系统错误

- [x] **42** 获取任务统计 `GET /tasks/stats`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `1011` - 系统错误

- [x] **43** 取消任务 `POST /tasks/{id}/cancel`
  - 请求参数：任务ID (路径参数), `reason` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `403` - 权限不足, `404` - 任务不存在, `1011` - 操作失败, `1011` - 系统错误

- [x] **44** 重试任务 `POST /tasks/{id}/retry`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `403` - 权限不足, `404` - 任务不存在, `1011` - 操作失败, `1011` - 系统错误

- [x] **45** 批量查询任务状态 `GET /tasks/batch/status`
  - 请求参数：`task_ids` (数组)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `1011` - 系统错误

- [x] **46** 查询任务恢复状态 `GET /tasks/{id}/recovery`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `1011` - 系统错误

- [x] **47** 获取超时配置 `GET /tasks/timeout-config`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `1011` - 系统错误

### **RecommendationController (8)**
- [x] **48** 获取内容推荐 `GET /recommendations/content`
  - 请求参数：`type, category, limit` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `1003` - 参数验证失败, `1011` - 系统错误

- [x] **49** 获取用户推荐 `GET /recommendations/users`
  - 请求参数：`type, limit` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `1003` - 参数验证失败, `1011` - 系统错误

- [x] **50** 获取话题推荐 `GET /recommendations/topics`
  - 请求参数：`type, category, limit` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `1003` - 参数验证失败, `1011` - 系统错误

- [x] **51** 提交推荐反馈 `POST /recommendations/feedback`
  - 请求参数：`recommendation_id, item_id, item_type, action, rating` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `1003` - 参数验证失败, `1011` - 系统错误

- [x] **52** 获取推荐偏好 `GET /recommendations/preferences`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `1011` - 系统错误

- [x] **53** 更新推荐偏好 `PUT /recommendations/preferences`
  - 请求参数：`preferences` (对象)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `1003` - 参数验证失败, `1011` - 系统错误

- [x] **54** 获取推荐分析 `GET /recommendations/analytics`
  - 请求参数：`period` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `1003` - 参数验证失败, `1011` - 系统错误

- [x] **55** 获取个性化推荐 `GET /recommendations/personalized`
  - 请求参数：无 (Header: Authorization)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `1011` - 系统错误

### **ProjectController (9)**
- [x] **56** 基于故事创建项目 `POST /projects/create-with-story`
  - 请求参数：`style_id, story_content, title` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `404` - 风格不存在, `1003` - 参数验证失败, `1010` - 比较失败, `1011` - 系统错误

- [x] **57** 确认项目标题 `PUT /projects/{id}/confirm-title`
  - 请求参数：项目ID (路径参数), `use_ai_title, custom_title` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `404` - 项目不存在, `1003` - 参数验证失败, `1011` - 操作失败, `1011` - 系统错误

- [x] **58** 获取我的项目列表 `GET /projects/my-projects`
  - 请求参数：`status, page, per_page` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `1011` - 系统错误

- [x] **59** 获取项目详情 `GET /projects/{id}`
  - 请求参数：项目ID (路径参数)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `404` - 项目不存在, `1011` - 系统错误

- [x] **60** 获取项目列表 `GET /projects/list`
  - 请求参数：`status, page, per_page` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `1011` - 系统错误

- [x] **61** 创建项目 `POST /projects/create`
  - 请求参数：`title, description, type` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `1003` - 参数验证失败, `1010` - 比较失败, `1011` - 系统错误

- [x] **62** 更新项目 `PUT /projects/{id}`
  - 请求参数：项目ID (路径参数), `title, description, status` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `404` - 项目不存在, `1003` - 参数验证失败, `1011` - 系统错误

- [x] **63** 删除项目 `DELETE /projects/{id}`
  - 请求参数：项目ID (路径参数)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `404` - 项目不存在, `1011` - 系统错误

### **FileController (5)**
- [x] **64** 上传文件 `POST /files/upload`
  - 请求参数：`file, folder, is_public, is_temporary` (folder、is_public、is_temporary可选)
  - 成功响应：`200` - 文件上传成功
  - 错误响应：`401` - 认证失败, `1010` - 文件上传失败, `1010` - 不支持的文件类型, `1011` - 系统错误

- [x] **65** 获取文件列表 `GET /files/list`
  - 请求参数：`folder, type, page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `1011` - 系统错误

- [x] **66** 获取文件详情 `GET /files/{id}`
  - 请求参数：文件ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 文件不存在, `1011` - 系统错误

- [x] **67** 删除文件 `DELETE /files/{id}`
  - 请求参数：文件ID (路径参数)
  - 成功响应：`200` - 文件删除成功
  - 错误响应：`401` - 认证失败, `404` - 文件不存在, `1011` - 系统错误

- [x] **68** 下载文件 `GET /files/{id}/download`
  - 请求参数：文件ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 文件不存在, `1007` - 文件已过期, `1011` - 系统错误

### **ImageController (4)**
- [x] **69** 生成图像 `POST /images/generate`
  - 请求参数：`prompt, character_id, project_id, generation_params` (可选)
  - 成功响应：`200` - 图像生成任务创建成功
  - 错误响应：`401` - 认证失败, `1010` - 参数验证失败, `404` - 角色不存在, `404` - 没有可用的图像生成模型, `1012` - 图像生成服务当前不可用, `1011` - 系统错误

- [x] **70** 获取图像生成状态 `GET /images/{id}/status`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `1011` - 系统错误

- [x] **71** 获取图像生成结果 `GET /images/{id}/result`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `1010` - 任务尚未完成, `1011` - 系统错误

- [x] **72** 批量生成图像 `POST /images/batch-generate`
  - 请求参数：`prompts, project_id, common_params` (可选)
  - 成功响应：`200` - 批量图像生成任务创建成功
  - 错误响应：`401` - 认证失败, `1010` - 参数验证失败, `1011` - 系统错误

### **VideoController (3)**
- [x] **73** 生成视频 `POST /videos/generate`
  - 请求参数：`prompt, duration, aspect_ratio, quality, fps, style, project_id, platform` (可选)
  - 成功响应：`200` - 视频生成任务创建成功
  - 错误响应：`401` - 认证失败, `404` - 没有可用的视频生成模型, `1012` - 视频生成服务当前不可用, `1011` - 系统错误

- [x] **74** 获取视频生成状态 `GET /videos/{id}/status`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - 获取视频状态成功
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `1011` - 获取视频状态失败

- [x] **75** 获取视频生成结果 `GET /videos/{id}/result`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - 获取视频结果成功
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `1010` - 任务尚未完成, `1011` - 获取视频结果失败

### **AudioController (4)**
- [x] **76** 音频混合 `POST /audio/mix`
  - 请求参数：`audio_urls, mix_config, project_id` (mix_config、project_id可选)
  - 成功响应：`200` - 音频混音任务创建成功
  - 错误响应：`401` - 认证失败, `1006` - 积分不足, `1011` - 系统错误

- [x] **77** 获取音频混合状态 `GET /audio/mix/{id}/status`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `1011` - 系统错误

- [x] **78** 音频增强 `POST /audio/enhance`
  - 请求参数：`audio_url, enhance_config, project_id` (enhance_config、project_id可选)
  - 成功响应：`200` - 音频增强任务创建成功
  - 错误响应：`401` - 认证失败, `1006` - 积分不足, `1011` - 系统错误

- [x] **79** 获取音频增强状态 `GET /audio/enhance/{id}/status`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `1011` - 系统错误

### **MusicController (4)**
- [x] **80** 生成音乐 `POST /music/generate`
  - 请求参数：`prompt, genre, mood, duration, project_id` (可选)
  - 成功响应：`200` - 音乐生成任务创建成功
  - 错误响应：`401` - 认证失败, `404` - 没有可用的音乐生成模型, `1012` - 音乐生成服务当前不可用, `1006` - 积分不足, `1011` - 系统错误

- [x] **81** 获取音乐生成状态 `GET /music/{id}/status`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `1011` - 系统错误

- [x] **82** 获取音乐生成结果 `GET /music/{id}/result`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `1010` - 任务尚未完成, `1011` - 系统错误

- [x] **83** 批量生成音乐 `POST /music/batch-generate`
  - 请求参数：`prompts, project_id, genre, mood` (可选)
  - 成功响应：`200` - 批量音乐生成任务创建成功
  - 错误响应：`401` - 认证失败, `1006` - 积分不足, `1011` - 系统错误

### **StoryController (2)**
- [x] **84** 生成故事 `POST /stories/generate`
  - 请求参数：`prompt, style_id, project_id, length, genre` (可选)
  - 成功响应：`200` - 故事生成任务创建成功
  - 错误响应：`401` - 认证失败, `1010` - 参数验证失败, `404` - 风格不存在, `404` - 没有可用的故事生成模型, `1012` - 故事生成服务当前不可用, `1006` - 积分不足, `1011` - 系统错误

- [x] **85** 获取故事生成状态 `GET /stories/{id}/status`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `1011` - 系统错误

### **CharacterController (9)**
- [x] **86** 获取角色分类 `GET /characters/categories`
  - 请求参数：`parent_id, include_children` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `1011` - 获取分类失败

- [x] **87** 获取角色库列表 `GET /characters/list`
  - 请求参数：`category_id, page, per_page, search` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `1011` - 获取角色列表失败

- [x] **88** 获取角色详情 `GET /characters/{id}`
  - 请求参数：角色ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `1010` - 无效的角色ID, `404` - 角色不存在, `1011` - 获取角色详情失败

- [x] **89** 生成角色 `POST /characters/generate`
  - 请求参数：`prompt, style, project_id` (可选)
  - 成功响应：`200` - 角色生成任务创建成功
  - 错误响应：`401` - 认证失败, `404` - 没有可用的角色生成模型, `1012` - 角色生成服务当前不可用, `1006` - 积分不足, `1011` - 系统错误

- [x] **90** 获取角色推荐 `GET /characters/recommendations`
  - 请求参数：`type, limit` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `1011` - 获取推荐失败

- [x] **91** 绑定角色 `POST /characters/bind`
  - 请求参数：`character_id, reason` (可选)
  - 成功响应：`200` - 角色绑定成功
  - 错误响应：`401` - 认证失败, `404` - 角色不存在, `1010` - 角色已经绑定, `1011` - 角色绑定失败

- [x] **92** 获取我的角色绑定 `GET /characters/bindings`
  - 请求参数：`page, per_page, status` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `1011` - 获取绑定列表失败

- [x] **93** 更新角色绑定 `PUT /characters/bindings/{id}`
  - 请求参数：绑定ID (路径参数), `binding_name, custom_description, custom_config, is_favorite` (可选)
  - 成功响应：`200` - 绑定更新成功
  - 错误响应：`401` - 认证失败, `1010` - 至少需要提供一个更新参数, `404` - 绑定不存在, `1011` - 绑定更新失败

- [x] **94** 解绑角色 `DELETE /characters/unbind`
  - 请求参数：`character_id`
  - 成功响应：`200` - 角色解绑成功
  - 错误响应：`401` - 认证失败, `404` - 角色不存在, `404` - 绑定不存在, `1013` - 绑定已解除, `1011` - 角色解绑失败

### **StyleController (4)**
- [x] **95** 获取风格列表 `GET /styles/list`
  - 请求参数：`page, per_page, category, search` (可选)
  - 成功响应：`200` - success
  - 错误响应：`1011` - 系统错误

- [x] **96** 获取风格详情 `GET /styles/{id}`
  - 请求参数：风格ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`404` - 风格不存在, `1011` - 系统错误

- [x] **97** 获取热门风格 `GET /styles/popular`
  - 请求参数：`limit` (可选)
  - 成功响应：`200` - success
  - 错误响应：`1011` - 系统错误

- [x] **98** 创建风格 `POST /styles/create`
  - 请求参数：`name, description, category, tags` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `1010` - 参数验证失败, `1011` - 创建风格失败

### **TemplateController (7)**
- [x] **99** 创建模板 `POST /templates/create`
  - 请求参数：`name, description, type, category, source_type, source_id, tags, visibility, configuration` (可选)
  - 成功响应：`200` - 模板创建成功
  - 错误响应：`401` - 认证失败, `404` - 来源资源或项目不存在, `1011` - 模板创建失败

- [x] **100** 使用模板 `POST /templates/{id}/use`
  - 请求参数：模板ID (路径参数), `name, customization, apply_settings` (可选)
  - 成功响应：`200` - 模板应用成功
  - 错误响应：`401` - 认证失败, `404` - 模板不存在或无权限使用, `1011` - 基于模板创建失败, `1011` - 模板使用失败

- [x] **101** 获取模板市场 `GET /templates/marketplace`
  - 请求参数：`category, type, sort, page, per_page, search` (可选)
  - 成功响应：`200` - success
  - 错误响应：`1011` - 获取模板市场失败

- [x] **102** 获取我的模板 `GET /templates/my-templates`
  - 请求参数：`type, category, visibility, page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `1011` - 获取用户模板失败

- [x] **103** 获取模板详情 `GET /templates/{id}/detail`
  - 请求参数：模板ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`404` - 模板不存在或无权限访问, `1011` - 获取模板详情失败

- [x] **104** 更新模板 `PUT /templates/{id}`
  - 请求参数：模板ID (路径参数), `name, description, tags, visibility, configuration` (可选)
  - 成功响应：`200` - 模板更新成功
  - 错误响应：`401` - 认证失败, `404` - 模板不存在或无权限修改, `1011` - 模板更新失败

- [x] **105** 删除模板 `DELETE /templates/{id}`
  - 请求参数：模板ID (路径参数)
  - 成功响应：`200` - 模板删除成功
  - 错误响应：`401` - 认证失败, `404` - 模板不存在或无权限删除, `1011` - 模板删除失败

### **VoiceController (7)**
- [x] **106** 语音合成 `POST /voice/synthesize`
  - 请求参数：`text, voice_id, character_id, project_id, speed, pitch, volume` (可选)
  - 成功响应：`200` - 语音合成任务创建成功
  - 错误响应：`401` - 认证失败, `404` - 角色不存在, `404` - 没有可用的语音合成模型, `1012` - 语音合成服务当前不可用, `1006` - 积分不足, `1011` - 语音合成失败

- [x] **107** 获取语音合成状态 `GET /voice/{id}/status`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `1011` - 系统错误

- [x] **108** 批量语音合成 `POST /voice/batch-synthesize`
  - 请求参数：`texts, project_id, voice_id, character_id, common_params` (可选)
  - 成功响应：`200` - 批量语音合成任务创建成功
  - 错误响应：`401` - 认证失败, `1006` - 积分不足, `1011` - 批量语音合成失败

- [x] **109** 语音克隆 `POST /voice/clone`
  - 请求参数：`audio_samples, voice_name, character_id, project_id` (可选)
  - 成功响应：`200` - 音色克隆任务创建成功
  - 错误响应：`401` - 认证失败, `404` - 角色不存在, `404` - 没有可用的音色克隆模型, `1006` - 积分不足, `1011` - 音色克隆失败

- [x] **110** 获取语音克隆状态 `GET /voice/clone/{id}/status`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `1011` - 系统错误

- [x] **111** 自定义语音 `POST /voice/custom`
  - 请求参数：`voice_description, reference_audio, character_id, project_id` (可选)
  - 成功响应：`200` - 自定义音色生成任务创建成功
  - 错误响应：`401` - 认证失败, `404` - 没有可用的音色生成模型, `1006` - 积分不足, `1011` - 自定义音色生成失败

- [x] **112** 获取自定义语音状态 `GET /voice/custom/{id}/status`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `1011` - 系统错误

### **AiGenerationController (4)**
- [x] **113** 文本生成 `POST /ai/text/generate`
  - 请求参数：`prompt, model, max_tokens, temperature, project_id` (可选)
  - 成功响应：`200` - 文本生成任务已创建
  - 错误响应：`401` - 认证失败, `1010` - 参数验证失败, `404` - 没有可用的文本生成模型, `1012` - 模型服务当前不可用, `1006` - 积分不足, `1011` - 文本生成任务创建失败

- [x] **114** 获取任务状态 `GET /ai/tasks/{id}`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `1011` - 获取任务状态失败

- [x] **115** 获取用户任务 `GET /ai/tasks`
  - 请求参数：`page, per_page, status` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `1011` - 获取任务列表失败

- [x] **116** 重试任务 `POST /ai/tasks/{id}/retry`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - 任务重试成功
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `1010` - 任务无法重试, `1011` - 任务重试失败

### **AiModelController (14)**
- [x] **117** 获取可用模型 `GET /ai-models/available`
  - 请求参数：`type, category, page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `1010` - 参数验证失败, `1011` - 系统错误

- [x] **118** 获取模型详情 `GET /ai-models/{model_id}/detail`
  - 请求参数：模型ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `1011` - 系统错误

- [x] **119** 测试模型 `POST /ai-models/{model_id}/test`
  - 请求参数：模型ID (路径参数), `test_data` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `1010` - 参数验证失败, `1011` - 系统错误

- [x] **120** 使用统计 `GET /ai-models/usage-stats`
  - 请求参数：`period, model_id` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `1011` - 系统错误

- [x] **121** 收藏模型 `POST /ai-models/{model_id}/favorite`
  - 请求参数：模型ID (路径参数), `action` (favorite/unfavorite)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `1011` - 系统错误

- [x] **122** 获取收藏列表 `GET /ai-models/favorites`
  - 请求参数：`page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `1011` - 系统错误

- [x] **123** 获取模型列表 `GET /ai-models/list`
  - 请求参数：`category, page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`1011` - 获取AI模型列表失败

- [x] **124** 切换模型 `POST /ai-models/switch`
  - 请求参数：`model_id, model_type`
  - 成功响应：`200` - AI模型切换成功
  - 错误响应：`401` - 认证失败, `1010` - 参数验证失败, `404` - 模型不存在或已禁用, `1010` - 模型类型不匹配, `1011` - 模型切换失败, `1011` - 切换AI模型失败

- [x] **125** 选择最优平台 `POST /ai-models/select-platform`
  - 请求参数：`platform_id, model_type`
  - 成功响应：`200` - success
  - 错误响应：`1011` - 平台选择失败

- [x] **126** 检查平台健康 `GET /ai-models/platform-health/{platform}`
  - 请求参数：无
  - 成功响应：`200` - success
  - 错误响应：`1011` - 健康检查失败

- [x] **127** 获取所有平台健康状态 `GET /ai-models/platforms-health`
  - 请求参数：无
  - 成功响应：`200` - success
  - 错误响应：`1011` - 获取健康状态失败

- [x] **128** 获取平台统计 `GET /ai-models/platform-stats/{platform}`
  - 请求参数：无
  - 成功响应：`200` - success
  - 错误响应：`1011` - 获取统计数据失败

- [x] **129** 平台性能对比 `GET /ai-models/platform-comparison`
  - 请求参数：`platforms, metrics` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 请登录后操作, `1011` - 获取平台对比数据失败

- [x] **130** 按业务类型获取平台 `GET /ai-models/business-platforms`
  - 请求参数：`business_type, requirements` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 请登录后操作, `1011` - 获取平台列表失败

### **SoundController (4)**
- [x] **131** 生成音效 `POST /sounds/generate`
  - 请求参数：`prompt, duration, style, project_id` (可选)
  - 成功响应：`200` - 音效生成任务创建成功
  - 错误响应：`401` - 认证失败, `404` - 没有可用的音效生成模型, `1012` - 音效生成服务当前不可用, `1006` - 积分不足, `1011` - 音效生成失败

- [x] **132** 获取生成状态 `GET /sounds/{id}/status`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `1011` - 系统错误

- [x] **133** 获取生成结果 `GET /sounds/{id}/result`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 任务不存在, `1010` - 任务尚未完成, `1011` - 系统错误

- [x] **134** 批量生成音效 `POST /sounds/batch-generate`
  - 请求参数：`prompts, project_id, common_params` (可选)
  - 成功响应：`200` - 批量音效生成任务创建成功
  - 错误响应：`401` - 认证失败, `1006` - 积分不足, `1011` - 批量音效生成失败

### **ProjectManagementController (6)**
- [x] **135** 创建项目任务 `POST /project-management/tasks`
  - 请求参数：`title, description, priority, deadline, project_id` (可选)
  - 成功响应：`200` - 任务创建成功
  - 错误响应：`401` - 认证失败, `1010` - 参数验证失败, `1011` - 任务创建失败

- [x] **136** 项目协作管理 `POST /project-management/collaborate`
  - 请求参数：`project_id, user_id, role, permissions` (可选)
  - 成功响应：`200` - 协作管理成功
  - 错误响应：`401` - 认证失败, `404` - 项目不存在, `1010` - 参数验证失败, `1011` - 协作管理失败

- [x] **137** 获取项目进度 `GET /project-management/progress`
  - 请求参数：`project_id, period` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 项目不存在, `1011` - 获取项目进度失败

- [x] **138** 分配项目资源 `POST /project-management/assign-resources`
  - 请求参数：`project_id, resource_type, resource_id, allocation, resource_data` (可选)
  - 成功响应：`200` - 资源分配成功
  - 错误响应：`401` - 认证失败, `404` - 项目不存在, `1010` - 参数验证失败, `1011` - 资源分配失败

- [x] **139** 获取项目统计 `GET /project-management/statistics`
  - 请求参数：`project_id, metrics, period` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 项目不存在, `1011` - 获取项目统计失败

- [x] **140** 获取项目里程碑 `GET /project-management/milestones`
  - 请求参数：`project_id, status` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 项目不存在, `1011` - 获取项目里程碑失败

### **ResourceController (8)**
- [x] **141** 生成资源 `POST /resources/generate`
  - 请求参数：`type, parameters, project_id` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 未授权, `422` - 参数错误, `503` - 服务不可用, `1012` - 积分不足, `1011` - 系统错误

- [x] **142** 获取资源状态 `GET /resources/{id}/status`
  - 请求参数：资源ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 资源不存在或无权限访问, `1011` - 系统错误

- [x] **143** 获取资源列表 `GET /resources/list`
  - 请求参数：`type, status, page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `1011` - 获取资源列表失败

- [x] **144** 删除资源 `DELETE /resources/{id}`
  - 请求参数：资源ID (路径参数)
  - 成功响应：`200` - 资源删除成功
  - 错误响应：`401` - 认证失败, `404` - 资源不存在或无权限访问, `1011` - 资源删除失败

- [x] **145** 获取下载信息 `GET /resources/{id}/download-info`
  - 请求参数：资源ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 资源不存在或无权限访问, `1011` - 系统错误

- [x] **146** 确认下载 `POST /resources/{id}/confirm-download`
  - 请求参数：资源ID (路径参数), `download_type` (可选)
  - 成功响应：`200` - 下载确认成功
  - 错误响应：`401` - 认证失败, `404` - 资源不存在或无权限访问, `1010` - 参数验证失败, `1011` - 下载确认失败

- [x] **147** 获取我的资源 `GET /resources/my-resources`
  - 请求参数：`type, status, page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `1011` - 获取我的资源失败

- [x] **148** 更新资源状态 `PUT /resources/{id}/status`
  - 请求参数：资源ID (路径参数), `status, reason` (可选)
  - 成功响应：`200` - 资源状态更新成功
  - 错误响应：`401` - 认证失败, `404` - 资源不存在或无权限访问, `1010` - 参数验证失败, `1011` - 资源状态更新失败

### **AssetController (4)**
- [x] **149** 获取素材列表 `GET /assets/list`
  - 请求参数：`category, file_type, page, per_page` (可选)
  - 成功响应：`200` - 获取素材列表成功
  - 错误响应：`1011` - 获取素材列表失败

- [x] **150** 上传素材 `POST /assets/upload`
  - 请求参数：`file, file_type, category, description, url, title` (可选)
  - 成功响应：`200` - 素材上传成功
  - 错误响应：`1010` - 参数验证失败, `1011` - 素材上传失败

- [x] **151** 获取素材详情 `GET /assets/{id}`
  - 请求参数：素材ID (路径参数)
  - 成功响应：`200` - 获取素材详情成功
  - 错误响应：`404` - 素材不存在, `1011` - 获取素材详情失败

- [x] **152** 删除素材 `DELETE /assets/{id}`
  - 请求参数：素材ID (路径参数)
  - 成功响应：`200` - 删除素材成功
  - 错误响应：`404` - 素材不存在, `1011` - 删除素材失败

### **PublicationController (8)**
- [x] **153** 发布作品 `POST /publications/publish`
  - 请求参数：`work_id, title, description, tags, visibility` (可选)
  - 成功响应：`200` - 作品发布成功
  - 错误响应：`401` - 认证失败, `404` - 资源不存在或未完成, `1010` - 该资源已发布或正在审核中, `1011` - 作品发布失败

- [x] **154** 获取发布状态 `GET /publications/{id}/status`
  - 请求参数：发布ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 发布记录不存在, `1011` - 获取发布状态失败

- [x] **155** 更新发布信息 `PUT /publications/{id}`
  - 请求参数：发布ID (路径参数), `title, description, tags, visibility` (可选)
  - 成功响应：`200` - 作品信息更新成功
  - 错误响应：`401` - 认证失败, `404` - 发布记录不存在, `1010` - 作品审核中，不能修改核心信息, `1011` - 更新发布信息失败

- [x] **156** 删除发布 `DELETE /publications/{id}`
  - 请求参数：发布ID (路径参数)
  - 成功响应：`200` - 作品取消发布成功
  - 错误响应：`401` - 认证失败, `404` - 发布记录不存在, `1011` - 取消发布失败

- [x] **157** 取消发布 `POST /publications/{id}/unpublish`
  - 请求参数：发布ID (路径参数), `reason` (可选)
  - 成功响应：`200` - 作品取消发布成功
  - 错误响应：`401` - 认证失败, `404` - 发布记录不存在, `1011` - 取消发布失败

- [x] **158** 获取我的发布 `GET /publications/my-publications`
  - 请求参数：`status, category, page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `1011` - 获取发布列表失败

- [x] **159** 获取发布广场 `GET /publications/plaza`
  - 请求参数：`category, sort, tags, page, per_page` (可选)
  - 成功响应：`200` - success
  - 错误响应：`1011` - 获取作品广场失败

- [x] **160** 获取发布详情 `GET /publications/{id}/detail`
  - 请求参数：发布ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`404` - 作品不存在或不可访问, `1011` - 获取作品详情失败

### **AnalyticsController (6)**
- [x] **161** 获取用户行为分析 `GET /analytics/user-behavior`
  - 请求参数：`period, user_id, metrics` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `1013` - 权限不足，只能分析自己的行为数据, `1011` - 用户行为分析失败

- [x] **162** 获取系统使用分析 `GET /analytics/system-usage`
  - 请求参数：`period, granularity` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `1013` - 权限不足，仅管理员可查看系统统计, `1011` - 系统统计分析失败

- [x] **163** 获取AI性能分析 `GET /analytics/ai-performance`
  - 请求参数：`period, model_type, metrics` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `1013` - 权限不足，仅管理员可查看AI性能分析, `1011` - AI性能分析失败

- [x] **164** 获取用户留存分析 `GET /analytics/user-retention`
  - 请求参数：`period, cohort_type` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `1013` - 权限不足，仅管理员可查看留存分析, `1011` - 留存分析失败

- [x] **165** 获取收入分析 `GET /analytics/revenue`
  - 请求参数：`period, breakdown_by` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `1013` - 权限不足，仅管理员可查看收入分析, `1011` - 收入分析失败

- [x] **166** 生成自定义报告 `POST /analytics/custom-report`
  - 请求参数：`report_type, parameters, format` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `1013` - 权限不足，仅管理员可生成自定义报告, `1011` - 自定义报告生成失败

### **BatchController (7)**
- [x] **167** 批量生成图片 `POST /batch/images/generate`
  - 请求参数：`prompts, settings, project_id` (可选)
  - 成功响应：`200` - 批处理任务创建成功
  - 错误响应：`401` - 认证失败, `1011` - 批处理任务创建失败

- [x] **168** 批量合成语音 `POST /batch/voices/synthesize`
  - 请求参数：`texts, voice_settings, project_id` (可选)
  - 成功响应：`200` - 批处理任务创建成功
  - 错误响应：`401` - 认证失败, `1011` - 批处理任务创建失败

- [x] **169** 批量生成音乐 `POST /batch/music/generate`
  - 请求参数：`prompts, music_settings, project_id` (可选)
  - 成功响应：`200` - 批处理任务创建成功
  - 错误响应：`401` - 认证失败, `1011` - 批处理任务创建失败

- [x] **170** 获取批量状态 `GET /batch/tasks/status`
  - 请求参数：`batch_id, task_type` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `404` - 批处理任务不存在, `1013` - 无权访问此批量任务, `1011` - 批处理任务状态获取失败

- [x] **171** 取消批量任务 `POST /batch/tasks/cancel`
  - 请求参数：`batch_id, reason` (可选)
  - 成功响应：`200` - 批量任务取消成功
  - 错误响应：`401` - 认证失败, `404` - 批处理任务不存在, `1013` - 无权操作此批量任务, `1007` - 批处理任务已完成或已取消, `1011` - 批处理任务取消失败

- [x] **172** 批量生成资源 `POST /batch/resources/generate`
  - 请求参数：`resources, type, settings` (可选)
  - 成功响应：`200` - 批量任务创建成功
  - 错误响应：`401` - 请登录后操作, `1011` - 创建批量任务失败

- [x] **173** 获取资源状态 `GET /batch/resources/status`
  - 请求参数：`task_id` (可选)
  - 成功响应：`200` - success
  - 错误响应：`401` - 请登录后操作, `1011` - 获取任务状态失败

### **WorkflowController (8)**
- [x] **174** 创建工作流 `POST /workflows/create`
  - 请求参数：`name, description, steps, triggers` (可选)
  - 成功响应：`200` - 工作流创建成功
  - 错误响应：`401` - 认证失败, `1011` - 工作流创建失败

- [x] **175** 获取工作流列表 `GET /workflows`
  - 请求参数：`status, category, page, per_page` (可选)
  - 成功响应：`200` - 工作流列表获取成功
  - 错误响应：`401` - 认证失败, `1011` - 工作流列表获取失败

- [x] **176** 获取工作流详情 `GET /workflows/{id}`
  - 请求参数：工作流ID (路径参数)
  - 成功响应：`200` - 工作流详情获取成功
  - 错误响应：`401` - 认证失败, `404` - 工作流不存在, `403` - 无权限访问此工作流, `1011` - 工作流详情获取失败

- [x] **177** 执行工作流 `POST /workflows/{id}/execute`
  - 请求参数：工作流ID (路径参数), `input_data` (可选)
  - 成功响应：`200` - 工作流执行启动成功
  - 错误响应：`401` - 认证失败, `404` - 工作流不存在, `400` - 工作流未激活，无法执行, `1011` - 工作流执行失败

- [x] **178** 获取执行状态 `GET /workflows/{id}/execution-status`
  - 请求参数：执行ID (路径参数)
  - 成功响应：`200` - 执行状态获取成功
  - 错误响应：`401` - 认证失败, `1011` - 执行状态获取失败

- [x] **179** 提供步骤输入 `POST /workflows/{id}/step-input`
  - 请求参数：执行ID (路径参数), `step_data` (可选)
  - 成功响应：`200` - 输入数据提交成功，工作流继续执行
  - 错误响应：`401` - 认证失败, `1011` - 步骤输入提交失败

- [x] **180** 取消执行 `POST /workflows/{id}/cancel`
  - 请求参数：执行ID (路径参数), `reason` (可选)
  - 成功响应：`200` - 工作流执行取消成功
  - 错误响应：`401` - 认证失败, `1011` - 工作流执行取消失败

- [x] **181** 获取执行历史 `GET /workflows/{id}/history`
  - 请求参数：工作流ID (路径参数), `page, per_page` (可选)
  - 成功响应：`200` - 执行历史获取成功
  - 错误响应：`401` - 认证失败, `1011` - 执行历史获取失败

### **PermissionController (7)**
- [x] **182** 获取用户权限 `GET /permissions/user/{id}`
  - 请求参数：用户ID (路径参数)
  - 成功响应：`200` - success
  - 错误响应：`401` - 认证失败, `1013` - 权限不足，只能查看自己的权限信息, `1011` - 系统错误

- [x] **183** 检查权限 `POST /permissions/check`
  - 请求参数：`permission, resource_id` (可选)
  - 成功响应：`200` - 权限验证通过
  - 错误响应：`401` - 认证失败, `1013` - 权限不足，只能检查自己的权限, `1011` - 权限检查失败

- [x] **184** 获取角色列表 `GET /permissions/roles`
  - 请求参数：`page, per_page` (可选)
  - 成功响应：`200` - 角色列表获取成功
  - 错误响应：`401` - 认证失败, `1013` - 权限不足，仅管理员可查看角色列表, `1011` - 角色列表获取失败

- [x] **185** 分配角色 `PUT /permissions/assign-role`
  - 请求参数：`user_id, role_id, reason` (可选)
  - 成功响应：`200` - 角色分配成功
  - 错误响应：`401` - 认证失败, `1013` - 权限不足，仅管理员可分配角色, `1010` - 无效的角色, `1011` - 角色分配失败

- [x] **186** 授予权限 `POST /permissions/grant`
  - 请求参数：`user_id, permissions, reason` (可选)
  - 成功响应：`200` - 权限授予成功
  - 错误响应：`401` - 认证失败, `1013` - 权限不足，仅管理员可授予权限, `1011` - 权限授予失败

- [x] **187** 撤销权限 `DELETE /permissions/revoke`
  - 请求参数：`user_id, permissions, reason` (可选)
  - 成功响应：`200` - 权限撤销成功
  - 错误响应：`401` - 认证失败, `1013` - 权限不足，仅管理员可撤销权限, `1011` - 权限撤销失败

- [x] **188** 获取权限历史 `GET /permissions/history`
  - 请求参数：`user_id, action_type, page, per_page` (可选)
  - 成功响应：`200` - 权限历史获取成功
  - 错误响应：`401` - 认证失败, `1013` - 权限不足，仅管理员可查看权限历史, `1011` - 获取权限历史失败

### **CacheController (8)**
- [ ] **189** 获取缓存统计 `GET /cache/stats`
  - 请求参数：`type, period` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 未授权, `403` - 权限不足, `422` - 参数错误, `1011` - 系统错误

- [ ] **190** 清除缓存 `DELETE /cache/clear`
  - 请求参数：`pattern, force` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 未授权, `403` - 权限不足, `422` - 参数错误, `1011` - 系统错误

- [ ] **191** 预热缓存 `POST /cache/warmup`
  - 请求参数：`keys, priority` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 未授权, `403` - 权限不足, `422` - 参数错误, `1011` - 系统错误

- [ ] **192** 获取缓存键 `GET /cache/keys`
  - 请求参数：`pattern, limit` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 未授权, `403` - 权限不足, `422` - 参数错误, `1011` - 系统错误

- [ ] **193** 获取缓存值 `GET /cache/value`
  - 请求参数：`key`
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 未授权, `403` - 权限不足, `404` - 缓存不存在, `422` - 参数错误, `1011` - 系统错误

- [ ] **194** 设置缓存值 `PUT /cache/value`
  - 请求参数：`key, value, ttl` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 未授权, `403` - 权限不足, `422` - 参数错误, `1011` - 系统错误

- [ ] **195** 删除缓存键 `DELETE /cache/keys`
  - 请求参数：`keys` (数组)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 未授权, `403` - 权限不足, `404` - 缓存不存在, `422` - 参数错误, `1011` - 系统错误

- [ ] **196** 获取缓存配置 `GET /cache/config`
  - 请求参数：无
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 未授权, `403` - 权限不足, `1011` - 系统错误

### **LogController (6)**
- [ ] **197** 获取系统日志 `GET /logs/system`
  - 请求参数：`level, start_date, end_date, page, per_page` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 未授权, `403` - 权限不足, `422` - 参数错误, `1011` - 系统错误

- [ ] **198** 获取用户行为日志 `GET /logs/user-actions`
  - 请求参数：`user_id, action_type, start_date, end_date, page, per_page` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 未授权, `403` - 权限不足, `422` - 参数错误, `1011` - 系统错误

- [ ] **199** 获取AI调用日志 `GET /logs/ai-calls`
  - 请求参数：`model_type, status, start_date, end_date, page, per_page` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 未授权, `403` - 权限不足, `422` - 参数错误, `1011` - 系统错误

- [ ] **200** 获取错误日志 `GET /logs/errors`
  - 请求参数：`level, resolved, start_date, end_date, page, per_page` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 未授权, `403` - 权限不足, `422` - 参数错误, `1011` - 系统错误

- [ ] **201** 解决错误 `POST /logs/errors/{id}/resolve`
  - 请求参数：错误ID (路径参数), `solution, notes` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 未授权, `403` - 权限不足, `404` - 错误不存在, `422` - 参数错误, `1011` - 系统错误

- [ ] **202** 导出日志 `POST /logs/export`
  - 请求参数：`log_type, format, start_date, end_date` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 未授权, `403` - 权限不足, `422` - 参数错误, `1011` - 系统错误

### **AdController (2)**
- [ ] **203** 广告开始 `POST /ad.store`
  - 请求参数：`ad_id`
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 未授权, `422` - 参数错误, `1011` - 系统错误

- [ ] **204** 广告更新 `POST /ad.update`
  - 请求参数：广告相关参数
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 未授权, `422` - 参数错误, `1011` - 系统错误

### **DownloadController (7)**
- [ ] **205** 获取下载列表 `GET /downloads/list`
  - 请求参数：`download_type, status, date_from, date_to, page, per_page` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 未授权, `422` - 参数错误, `1011` - 系统错误

- [ ] **206** 重试下载 `POST /downloads/{id}/retry`
  - 请求参数：下载ID (路径参数)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 未授权, `404` - 下载不存在, `422` - 参数错误, `1011` - 系统错误

- [ ] **207** 获取下载统计 `GET /downloads/statistics`
  - 请求参数：`period, download_type` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 未授权, `422` - 参数错误, `1011` - 系统错误

- [ ] **208** 创建下载链接 `POST /downloads/create-link`
  - 请求参数：`target_type, target_id, expires_in` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 未授权, `404` - 目标不存在, `422` - 参数错误, `1011` - 系统错误

- [ ] **209** 安全下载 `GET /downloads/secure/{token}`
  - 请求参数：下载令牌 (路径参数)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 未授权, `404` - 令牌无效, `410` - 链接已过期, `1011` - 系统错误

- [ ] **210** 批量下载 `POST /downloads/batch`
  - 请求参数：`items` (数组)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 未授权, `422` - 参数错误, `1011` - 系统错误

- [ ] **211** 清理下载 `POST /downloads/cleanup`
  - 请求参数：`days_old, cleanup_files` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 未授权, `403` - 权限不足, `422` - 参数错误, `1011` - 系统错误

### **ReviewController (7)**
- [ ] **212** 提交审核 `POST /reviews/submit`
  - 请求参数：`publication_id, review_type, additional_info` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 未授权, `404` - 发布不存在, `422` - 参数错误, `1008` - 重复操作, `1011` - 系统错误

- [ ] **213** 获取审核状态 `GET /reviews/{id}/status`
  - 请求参数：审核ID (路径参数)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 未授权, `404` - 审核不存在, `1011` - 系统错误

- [ ] **214** 申诉审核 `POST /reviews/{id}/appeal`
  - 请求参数：审核ID (路径参数), `appeal_reason, additional_evidence` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 未授权, `404` - 审核不存在, `422` - 参数错误, `1007` - 无效操作, `1002` - 已过期, `1003` - 超出限制, `1011` - 系统错误

- [ ] **215** 获取我的审核 `GET /reviews/my-reviews`
  - 请求参数：`status, review_type, date_from, date_to, page, per_page` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 未授权, `422` - 参数错误, `1011` - 系统错误

- [ ] **216** 获取队列状态 `GET /reviews/queue-status`
  - 请求参数：无
  - 成功响应：`200` - 成功
  - 错误响应：`1011` - 系统错误

- [ ] **217** 获取审核指南 `GET /reviews/guidelines`
  - 请求参数：无
  - 成功响应：`200` - 成功
  - 错误响应：无

- [ ] **218** 预检查 `POST /reviews/pre-check`
  - 请求参数：`resource_id, title, description, tags` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 未授权, `422` - 参数错误, `1011` - 系统错误

### **SocialController (9)**
- [ ] **219** 关注/取消关注 `POST /social/follow`
  - 请求参数：`target_user_id, action`
  - 成功响应：`200` - 操作成功
  - 错误响应：`401` - 未登录, `422` - 参数验证失败, `1010` - 参数无效（不能关注自己）, `1008` - 重复操作（已经关注该用户）, `404` - 资源不存在（未关注该用户）, `1011` - 系统错误

- [ ] **220** 获取关注列表 `GET /social/follows`
  - 请求参数：`user_id, type, page, per_page` (可选)
  - 成功响应：`200` - 获取成功
  - 错误响应：`422` - 参数验证失败, `1011` - 系统错误

- [ ] **221** 点赞/取消点赞 `POST /social/like`
  - 请求参数：`target_type, target_id, action`
  - 成功响应：`200` - 操作成功
  - 错误响应：`401` - 未登录, `422` - 参数验证失败, `404` - 资源不存在（目标内容不存在或未点赞过该内容）, `1011` - 系统错误

- [ ] **222** 评论 `POST /social/comment`
  - 请求参数：`target_type, target_id, content, parent_id` (可选)
  - 成功响应：`200` - 评论成功
  - 错误响应：`401` - 未登录, `422` - 参数验证失败, `1011` - 系统错误

- [ ] **223** 获取评论列表 `GET /social/{id}/comments`
  - 请求参数：目标ID (路径参数), `target_type, sort, page, per_page` (可选)
  - 成功响应：`200` - 获取成功
  - 错误响应：`422` - 参数验证失败, `1011` - 系统错误

- [ ] **224** 分享 `POST /social/share`
  - 请求参数：`target_type, target_id, platform, message` (可选)
  - 成功响应：`200` - 分享成功
  - 错误响应：`401` - 未登录, `422` - 参数验证失败, `404` - 资源不存在（分享目标不存在）, `1011` - 系统错误

- [ ] **225** 获取动态流 `GET /social/feed`
  - 请求参数：`type, page, per_page` (可选)
  - 成功响应：`200` - 获取成功
  - 错误响应：`401` - 未登录, `422` - 参数验证失败, `1011` - 系统错误

- [ ] **226** 获取社交通知 `GET /social/notifications`
  - 请求参数：`type, status, page, per_page` (可选)
  - 成功响应：`200` - 获取成功
  - 错误响应：`401` - 未登录, `422` - 参数验证失败, `1011` - 系统错误

- [ ] **227** 标记通知已读 `PUT /social/notifications/read`
  - 请求参数：`notification_ids` (可选)
  - 成功响应：`200` - 标记成功
  - 错误响应：`401` - 未登录, `422` - 参数验证失败, `1011` - 系统错误

### **TaskManagementController (5)**
- [ ] **228** 取消任务 `POST /tasks/{id}/cancel`
  - 请求参数：任务ID (路径参数), `reason` (可选)
  - 成功响应：`200` - 任务已取消
  - 错误响应：`401` - 认证失败, `1002` - 用户无权取消该任务, `404` - 任务不存在, `409` - 任务已完成或已取消, `422` - 取消原因不能超过500个字符, `1011` - 任务取消失败

- [ ] **229** 重试任务 `POST /tasks/{id}/retry`
  - 请求参数：任务ID (路径参数), `platform` (可选)
  - 成功响应：`200` - 任务重试成功
  - 错误响应：`401` - 认证失败, `1002` - 用户无权重试该任务, `404` - 任务不存在, `409` - 任务状态不允许重试, `422` - AI平台必须是：deepseek、liblib、kling、minimax、volcengine之一, `1007` - 任务重试次数已达上限, `1011` - 任务重试失败

- [ ] **230** 获取批量状态 `GET /tasks/batch/status`
  - 请求参数：`task_ids` (string, 任务ID列表), `batch_id` (string, 批量任务ID)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `422` - 任务ID列表或批量任务ID必须提供其中一个, `1003` - 任务ID列表格式错误

- [ ] **231** 获取超时配置 `GET /tasks/timeout-config`
  - 请求参数：无
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `1001` - 获取超时配置失败

- [ ] **232** 查询任务恢复状态 `GET /tasks/{id}/recovery`
  - 请求参数：任务ID (路径参数)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `1002` - 权限不足, `404` - 任务不存在, `1010` - 任务ID无效, `1001` - 查询恢复状态失败



### **VersionController (6)**
- [ ] **233** 创建版本 `POST /resources/{id}/versions`
  - 请求参数：资源ID (路径参数), `version_name, description, generation_config, base_version_id` (可选)
  - 成功响应：`200` - 版本创建成功
  - 错误响应：`401` - 认证失败, `404` - 资源不存在或无权限访问, `1003` - 生成配置不能为空/生成提示词不能为空/生成提示词至少5个字符/生成提示词不能超过2000个字符/基础版本不存在/版本名称不能超过100个字符/版本描述不能超过500个字符, `1004` - 积分不足, `1001` - 版本创建失败

- [ ] **234** 获取版本列表 `GET /resources/{id}/versions`
  - 请求参数：资源ID (路径参数), `page, per_page, status` (可选)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `404` - 资源不存在或无权限访问, `1003` - 参数验证失败, `1001` - 获取版本历史失败

- [ ] **235** 获取版本详情 `GET /versions/{id}`
  - 请求参数：版本ID (路径参数)
  - 成功响应：`200` - 成功
  - 错误响应：`401` - 认证失败, `404` - 版本不存在或无权限访问, `1001` - 获取版本详情失败

- [ ] **236** 设置当前版本 `PUT /versions/{id}/set-current`
  - 请求参数：版本ID (路径参数)
  - 成功响应：`200` - 当前版本设置成功
  - 错误响应：`401` - 认证失败, `404` - 版本不存在或无权限访问, `1011` - 只能设置已完成的版本为当前版本, `1001` - 设置当前版本失败

- [ ] **237** 删除版本 `DELETE /versions/{id}`
  - 请求参数：版本ID (路径参数)
  - 成功响应：`200` - 版本删除成功
  - 错误响应：`401` - 认证失败, `404` - 版本不存在或无权限访问, `1011` - 不能删除当前版本, `1001` - 版本删除失败

- [ ] **238** 比较版本 `GET /versions/compare`
  - 请求参数：`version1_id, version2_id`
  - 成功响应：`200` - 版本比较完成
  - 错误响应：`401` - 认证失败, `404` - 版本不存在或无权限访问, `1003` - 版本1 ID不能为空/版本1不存在/版本2 ID不能为空/版本2不存在, `1010` - 只能比较同一资源的不同版本, `1001` - 版本比较失败

### **WebSocketController (4)**
- [ ] **239** WebSocket认证 `POST /websocket/auth`
  - 请求参数：`client_type, client_info` (可选) (Header: Authorization)
  - 成功响应：`200` - 认证成功
  - 错误响应：`401` - 认证失败, `403` - WEB工具禁用WebSocket连接, `1003` - 客户端类型不能为空/客户端类型无效, `1001` - 连接认证失败, `1012` - 用户连接数已达上限, `1003` - 无效的客户端类型, `1001` - 系统错误

- [ ] **240** 获取WebSocket会话 `GET /websocket/sessions`
  - 请求参数：`status, client_type` (可选)
  - 成功响应：`200` - 获取成功
  - 错误响应：`401` - 认证失败, `1001` - 获取会话列表失败, `1001` - 系统错误

- [ ] **241** 断开WebSocket连接 `POST /websocket/disconnect`
  - 请求参数：`session_id, reason` (可选)
  - 成功响应：`200` - 连接已断开
  - 错误响应：`401` - 认证失败, `404` - 会话不存在, `1003` - 会话ID不能为空, `1001` - 断开连接失败, `1011` - 会话已断开, `1001` - 系统错误

- [ ] **242** 获取WebSocket状态 `GET /websocket/status`
  - 请求参数：无
  - 成功响应：`200` - 获取成功
  - 错误响应：`1001` - 获取服务器状态失败, `1001` - 系统错误

### **WorkPublishController (8)**
- [ ] **243** 发布作品 `POST /works/publish`
  - 请求参数：`title, description, file_path, resource_id, status` (可选)
  - 成功响应：`200` - 作品发布成功
  - 错误响应：`401` - 认证失败, `403` - 发布权限不足, `404` - 资源不存在, `1003` - 参数验证失败, `1001` - 系统错误, `1011` - 权限被拒绝

- [ ] **244** 更新作品 `PUT /works/{id}`
  - 请求参数：作品ID (路径参数), `title, description, tags` (可选)
  - 成功响应：`200` - 作品更新成功
  - 错误响应：`401` - 认证失败, `1001` - 作品不存在或无权限, `1003` - 参数验证失败, `1001` - 系统错误, `1003` - 操作被限制

- [ ] **245** 删除作品 `DELETE /works/{id}`
  - 请求参数：作品ID (路径参数)
  - 成功响应：`200` - 作品删除成功
  - 错误响应：`401` - 认证失败, `1001` - 作品不存在或无权限, `1001` - 系统错误, `1003` - 操作被限制

- [ ] **246** 获取我的作品 `GET /works/my-works`
  - 请求参数：`work_type, publish_status, per_page` (可选)
  - 成功响应：`200` - 获取成功
  - 错误响应：`401` - 认证失败, `1001` - 系统错误

- [ ] **247** 作品展示库 `GET /works/gallery`
  - 请求参数：`work_type, featured, sort_by, per_page` (可选)
  - 成功响应：`200` - 获取成功
  - 错误响应：`1001` - 系统错误

- [ ] **248** 获取分享链接 `GET /works/{id}/share-link`
  - 请求参数：作品ID (路径参数), `type, password, expires_at, max_access_count` (可选)
  - 成功响应：`200` - 获取成功
  - 错误响应：`401` - 认证失败, `1001` - 作品不存在或无权限, `1003` - 参数验证失败, `1001` - 系统错误

- [ ] **249** 点赞作品 `POST /works/{id}/like`
  - 请求参数：作品ID (路径参数)
  - 成功响应：`200` - 点赞成功
  - 错误响应：`401` - 认证失败, `1001` - 作品不存在, `1001` - 系统错误, `1012` - 重复操作

- [ ] **250** 热门作品 `GET /works/trending`
  - 请求参数：`work_type, period, per_page` (可选)
  - 成功响应：`200` - 获取成功
  - 错误响应：`1001` - 系统错误
